# Generated by Django 5.2.4 on 2025-07-09 09:51

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('cart', '0001_initial'),
        ('staff', '0008_paymentoptionproxy_paypalorderproxy_wishlistproxy_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CartItemProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Cart Item',
                'verbose_name_plural': 'Staff Cart Items',
                'permissions': [('view_all_cart_items', 'Can view all cart items'), ('modify_cart_items', 'Can modify customer cart items')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('cart.cartitem',),
        ),
        migrations.CreateModel(
            name='CartProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Cart',
                'verbose_name_plural': 'Staff Carts',
                'permissions': [('view_all_carts', 'Can view all customer carts'), ('manage_abandoned_carts', 'Can manage abandoned carts'), ('bulk_cart_operations', 'Can perform bulk cart operations'), ('cart_analytics', 'Can view cart analytics')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('cart.cart',),
        ),
    ]
