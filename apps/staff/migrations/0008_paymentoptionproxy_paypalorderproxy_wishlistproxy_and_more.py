# Generated by Django 5.2.4 on 2025-07-09 09:39

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0003_alter_order_placed_at'),
        ('payments', '0002_alter_paypalorder_options_alter_paymentoption_name'),
        ('staff', '0007_addressproxy_customerproxy'),
        ('wishlist', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentOptionProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Payment Option',
                'verbose_name_plural': 'Staff Payment Options',
                'permissions': [('manage_payment_options', 'Can manage payment options'), ('payment_analytics', 'Can view payment analytics'), ('payment_monitoring', 'Can monitor payment transactions')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('payments.paymentoption',),
        ),
        migrations.CreateModel(
            name='PayPalOrderProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff PayPal Order',
                'verbose_name_plural': 'Staff PayPal Orders',
                'permissions': [('view_paypal_transactions', 'Can view PayPal transactions'), ('manage_paypal_disputes', 'Can manage PayPal disputes')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('payments.paypalorder',),
        ),
        migrations.CreateModel(
            name='WishlistProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Wishlist',
                'verbose_name_plural': 'Staff Wishlists',
                'permissions': [('view_all_wishlists', 'Can view all customer wishlists'), ('wishlist_analytics', 'Can view wishlist analytics'), ('customer_behavior_analysis', 'Can analyze customer behavior'), ('marketing_insights', 'Can access marketing insights'), ('bulk_wishlist_operations', 'Can perform bulk wishlist operations')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('wishlist.wishlist',),
        ),
        migrations.CreateModel(
            name='PaymentDispute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dispute_id', models.CharField(max_length=255, unique=True)),
                ('dispute_type', models.CharField(choices=[('CHARGEBACK', 'Chargeback'), ('INQUIRY', 'Inquiry'), ('CLAIM', 'Claim'), ('REFUND_REQUEST', 'Refund Request')], max_length=20)),
                ('status', models.CharField(choices=[('OPEN', 'Open'), ('UNDER_REVIEW', 'Under Review'), ('RESOLVED', 'Resolved'), ('CLOSED', 'Closed')], default='OPEN', max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reason', models.TextField()),
                ('gateway_data', models.JSONField(default=dict)),
                ('resolution_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_disputes', to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_disputes', to='order.order')),
            ],
            options={
                'verbose_name': 'Payment Dispute',
                'verbose_name_plural': 'Payment Disputes',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['dispute_type'], name='staff_payme_dispute_8466fb_idx'), models.Index(fields=['status'], name='staff_payme_status_8c3159_idx'), models.Index(fields=['created_at'], name='staff_payme_created_85a848_idx')],
            },
        ),
        migrations.CreateModel(
            name='PaymentTransactionAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(db_index=True, max_length=255)),
                ('payment_method', models.CharField(max_length=50)),
                ('order_id', models.IntegerField()),
                ('customer_id', models.IntegerField(blank=True, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(max_length=50)),
                ('gateway_response', models.JSONField(default=dict)),
                ('action', models.CharField(max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('staff_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_audits', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Payment Transaction Audit',
                'verbose_name_plural': 'Payment Transaction Audits',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['transaction_id'], name='staff_payme_transac_2394f6_idx'), models.Index(fields=['payment_method'], name='staff_payme_payment_29766f_idx'), models.Index(fields=['status'], name='staff_payme_status_1eb4a4_idx'), models.Index(fields=['created_at'], name='staff_payme_created_a50c94_idx')],
            },
        ),
    ]
