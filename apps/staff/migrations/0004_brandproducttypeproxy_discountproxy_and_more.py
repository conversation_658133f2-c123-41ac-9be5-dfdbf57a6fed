# Generated by Django 5.2.4 on 2025-07-06 13:09

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0010_alter_brand_slug_alter_category_slug'),
        ('staff', '0003_attributeproxy_attributevalueproxy_brandproxy_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BrandProductTypeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Brand Product Type',
                'verbose_name_plural': 'Staff Brand Product Types',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.brandproducttype',),
        ),
        migrations.CreateModel(
            name='DiscountProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Discount',
                'verbose_name_plural': 'Staff Discounts',
                'permissions': [('apply_discount', 'Can apply discounts to variants')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.discount',),
        ),
        migrations.CreateModel(
            name='ProductAttributeValueProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Attribute Value',
                'verbose_name_plural': 'Staff Product Attribute Values',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productattributevalue',),
        ),
        migrations.CreateModel(
            name='ProductImageProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Image',
                'verbose_name_plural': 'Staff Product Images',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productimage',),
        ),
        migrations.CreateModel(
            name='ProductTypeAttributeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Type Attribute',
                'verbose_name_plural': 'Staff Product Type Attributes',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.producttypeattribute',),
        ),
        migrations.CreateModel(
            name='ProductVariantAttributeValueProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Variant Attribute Value',
                'verbose_name_plural': 'Staff Product Variant Attribute Values',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productvariantattributevalue',),
        ),
        migrations.CreateModel(
            name='ProductVariantProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Variant',
                'verbose_name_plural': 'Staff Product Variants',
                'permissions': [('manage_variant_stock', 'Can manage variant stock'), ('manage_variant_ordering', 'Can manage variant ordering')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productvariant',),
        ),
        migrations.CreateModel(
            name='ReviewProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Review',
                'verbose_name_plural': 'Staff Reviews',
                'permissions': [('moderate_review', 'Can moderate reviews')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.review',),
        ),
    ]
