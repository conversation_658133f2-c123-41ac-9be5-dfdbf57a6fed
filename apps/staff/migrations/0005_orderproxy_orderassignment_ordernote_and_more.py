# Generated by Django 5.2.4 on 2025-07-08 20:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0003_alter_order_placed_at'),
        ('staff', '0004_brandproducttypeproxy_discountproxy_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Order',
                'verbose_name_plural': 'Staff Orders',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('order.order',),
        ),
        migrations.CreateModel(
            name='OrderAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, help_text='Assignment notes or instructions')),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders_assigned_by_me', to='staff.staffprofile')),
                ('assigned_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_orders', to='staff.staffprofile')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='order.order')),
            ],
            options={
                'verbose_name': 'Order Assignment',
                'verbose_name_plural': 'Order Assignments',
                'ordering': ['-assigned_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField(help_text='Internal staff note about the order')),
                ('is_internal', models.BooleanField(default=True, help_text='If False, note may be visible to customer')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='order_notes', to='staff.staffprofile')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_notes', to='order.order')),
            ],
            options={
                'verbose_name': 'Order Note',
                'verbose_name_plural': 'Order Notes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_status', models.CharField(max_length=20)),
                ('new_status', models.CharField(max_length=20)),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, help_text='Optional notes about the status change')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='order_status_changes', to='staff.staffprofile')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='order.order')),
            ],
            options={
                'verbose_name': 'Order Status History',
                'verbose_name_plural': 'Order Status Histories',
                'ordering': ['-changed_at'],
            },
        ),
    ]
