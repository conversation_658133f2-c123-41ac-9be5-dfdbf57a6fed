# Generated by Django 5.2.4 on 2025-07-09 09:23

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0001_initial'),
        ('staff', '0006_bulkorderoperation_orderdocument'),
    ]

    operations = [
        migrations.CreateModel(
            name='AddressProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Address',
                'verbose_name_plural': 'Staff Addresses',
                'permissions': [('view_all_addresses', 'Can view all customer addresses'), ('manage_customer_addresses', 'Can manage customer addresses')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('customers.address',),
        ),
        migrations.CreateModel(
            name='CustomerProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Customer',
                'verbose_name_plural': 'Staff Customers',
                'permissions': [('view_all_customers', 'Can view all customers'), ('manage_customer_data', 'Can manage customer data'), ('customer_analytics', 'Can view customer analytics'), ('bulk_customer_operations', 'Can perform bulk customer operations'), ('customer_support_access', 'Can access customer support features')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('customers.customer',),
        ),
    ]
