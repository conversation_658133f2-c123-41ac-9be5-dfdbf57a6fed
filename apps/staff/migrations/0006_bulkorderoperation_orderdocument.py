# Generated by Django 5.2.4 on 2025-07-09 03:40

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0003_alter_order_placed_at'),
        ('staff', '0005_orderproxy_orderassignment_ordernote_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BulkOrderOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('operation_type', models.CharField(choices=[('BULK_STATUS_UPDATE', 'Bulk Status Update'), ('BULK_ASSIGNMENT', 'Bulk Assignment'), ('BULK_NOTE_ADD', 'Bulk Note Addition'), ('BULK_LABEL_PRINT', 'Bulk Label Printing'), ('BULK_INVOICE_GENERATE', 'Bulk Invoice Generation'), ('BULK_WAREHOUSE_DOC', 'Bulk Warehouse Documentation')], max_length=30)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('total_items', models.PositiveIntegerField(default=0)),
                ('processed_items', models.PositiveIntegerField(default=0)),
                ('failed_items', models.PositiveIntegerField(default=0)),
                ('operation_data', models.JSONField(default=dict, help_text='Input data for the operation')),
                ('results', models.JSONField(default=dict, help_text='Results and output data')),
                ('error_message', models.TextField(blank=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bulk_order_operations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Bulk Order Operation',
                'verbose_name_plural': 'Bulk Order Operations',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('SHIPPING_LABEL', 'Shipping Label'), ('CUSTOMER_INVOICE', 'Customer Invoice'), ('WAREHOUSE_PICKUP', 'Warehouse Pickup Document'), ('PACKING_SLIP', 'Packing Slip'), ('DELIVERY_NOTE', 'Delivery Note')], max_length=20)),
                ('document_data', models.JSONField(default=dict, help_text='Document content and metadata')),
                ('file_path', models.CharField(blank=True, help_text='Path to generated file', max_length=500)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('is_printed', models.BooleanField(default=False)),
                ('printed_at', models.DateTimeField(blank=True, null=True)),
                ('bulk_operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_documents', to='staff.bulkorderoperation')),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_documents', to='staff.staffprofile')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='order.order')),
            ],
            options={
                'verbose_name': 'Order Document',
                'verbose_name_plural': 'Order Documents',
                'ordering': ['-generated_at'],
            },
        ),
    ]
