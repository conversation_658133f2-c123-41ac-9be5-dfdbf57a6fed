from rest_framework import serializers
from django.db import transaction
from .models import (
    ProductProxy, CategoryProxy, BrandProxy, ProductTypeProxy, AttributeProxy, AttributeValueProxy,
    ProductVariantProxy, ProductImageProxy, ReviewProxy, DiscountProxy, ProductTypeAttributeProxy,
    ProductAttributeValueProxy, ProductVariantAttributeValueProxy, BrandProductTypeProxy,
    ProductAudit, BulkProductOperation
)
from .services import ValidationService

"""
Staff Product Management Serializers

This module contains serializers for the staff product management interface.
The serializers follow a nested pattern for complex operations and provide
proper validation and type safety.

Key Improvements:
- Nested serializers for complex operations (e.g., AttributeAssociationSerializer)
- Proper boolean field handling instead of string conversion
- Comprehensive validation with meaningful error messages
- Type-safe field definitions with appropriate defaults
- Consistent API patterns across all bulk operations

Usage Examples:
- BulkAttributeAssociationSerializer: Accepts proper boolean values for flags
- AttributeAssociationSerializer: Validates individual attribute associations
- All bulk serializers: Include proper validation for referenced objects
"""


class CategoryStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff category management
    """
    children_count = serializers.SerializerMethodField()
    products_count = serializers.SerializerMethodField()
    full_path = serializers.SerializerMethodField()

    class Meta:
        model = CategoryProxy
        fields = [
            'id', 'title', 'slug', 'is_active', 'parent', 'level',
            'children_count', 'products_count', 'full_path'
        ]
        read_only_fields = ['level']

    def get_children_count(self, obj):
        return obj.get_children().count()

    def get_products_count(self, obj):
        return obj.products.count()

    def get_full_path(self, obj):
        return str(obj)  # Uses the __str__ method which shows the full path


class BrandStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff brand management
    """
    products_count = serializers.SerializerMethodField()
    product_types = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=ProductTypeProxy.objects.all(),
        required=False
    )

    class Meta:
        model = BrandProxy
        fields = ['id', 'title', 'slug', 'is_active', 'products_count', 'product_types']

    def get_products_count(self, obj):
        return obj.product_set.count()


class ProductTypeStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff product type management
    """
    attributes_count = serializers.SerializerMethodField()
    products_count = serializers.SerializerMethodField()

    class Meta:
        model = ProductTypeProxy
        fields = ['id', 'title', 'attributes_count', 'products_count']

    def get_attributes_count(self, obj):
        return obj.attribute.count()

    def get_products_count(self, obj):
        return obj.product_type.count()


class AttributeStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff attribute management
    """
    values_count = serializers.SerializerMethodField()
    product_types = serializers.SerializerMethodField()

    class Meta:
        model = AttributeProxy
        fields = ['id', 'title', 'values_count', 'product_types']

    def get_values_count(self, obj):
        return obj.attribute_value.count()

    def get_product_types(self, obj):
        return list(obj.product_type_attribute.values_list('product_type__title', flat=True))


class AttributeValueStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff attribute value management
    """
    attribute_title = serializers.CharField(source='attribute.title', read_only=True)
    products_count = serializers.SerializerMethodField()

    class Meta:
        model = AttributeValueProxy
        fields = ['id', 'attribute_value', 'attribute', 'attribute_title', 'is_active', 'products_count']

    def get_products_count(self, obj):
        return obj.product_attr_value.count()


class ProductImageStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff product image management
    """

    class Meta:
        model = ProductImageProxy
        fields = ['id', 'alternative_text', 'image', 'product_variant', 'order']
        read_only_fields = ['order']


class ProductVariantStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff product variant management
    """
    images = ProductImageStaffSerializer(source='product_image', many=True, read_only=True)
    attribute_values = AttributeValueStaffSerializer(
        source='attribute_value',
        many=True,
        read_only=True
    )
    price_label_title = serializers.CharField(source='price_label.attribute_value', read_only=True)

    class Meta:
        model = ProductVariantProxy
        fields = [
            'id', 'product', 'price', 'price_label', 'price_label_title', 'sku', 'stock_qty',
            'is_active', 'weight', 'condition', 'order', 'created_at', 'updated_at',
            'images', 'attribute_values'
        ]
        read_only_fields = ['order', 'created_at', 'updated_at']


class ProductStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff product management with full details
    """
    category_title = serializers.CharField(source='category.title', read_only=True)
    brand_title = serializers.CharField(source='brand.title', read_only=True)
    product_type_title = serializers.CharField(source='product_type.title', read_only=True)
    variants = ProductVariantStaffSerializer(source='product_variant', many=True, read_only=True)
    reviews_count = serializers.SerializerMethodField()
    # total_stock = serializers.SerializerMethodField()
    active_variants_count = serializers.SerializerMethodField()

    class Meta:
        model = ProductProxy
        fields = [
            'id', 'title', 'slug', 'brand', 'brand_title', 'description',
            'is_digital', 'is_active', 'product_type', 'product_type_title',
            'category', 'category_title', 'average_rating', 'created_at',
            'updated_at', 'variants', 'reviews_count',
            'active_variants_count'
        ]
        read_only_fields = ['average_rating', 'created_at', 'updated_at']

    def get_reviews_count(self, obj):
        return obj.reviews.count()

    # def get_total_stock(self, obj):
    #     return sum(variant.stock_qty for variant in obj.product_variant.all())

    def get_active_variants_count(self, obj):
        return obj.product_variant.filter(is_active=True).count()

    def validate(self, data):
        ValidationService.validate_product_data(data)
        return data


class ProductListStaffSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for product listing in the staff interface
    """
    category_title = serializers.CharField(source='category.title', read_only=True)
    brand_title = serializers.CharField(source='brand.title', read_only=True)
    product_type_title = serializers.CharField(source='product_type.title', read_only=True)
    variants_count = serializers.SerializerMethodField()
    total_stock = serializers.SerializerMethodField()

    class Meta:
        model = ProductProxy
        fields = [
            'id', 'title', 'slug', 'brand_title', 'product_type_title',
            'category_title', 'is_active', 'average_rating', 'variants_count',
            'total_stock', 'created_at', 'updated_at'
        ]

    def get_variants_count(self, obj):
        return obj.product_variant.count()

    def get_total_stock(self, obj):
        return sum(variant.stock_qty for variant in obj.product_variant.all())


class ProductTypeAttributeStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for product type attribute associations
    """
    attribute_title = serializers.CharField(source='attribute.title', read_only=True)
    product_type_title = serializers.CharField(source='product_type.title', read_only=True)

    class Meta:
        model = ProductTypeAttributeProxy
        fields = [
            'id', 'product_type', 'product_type_title', 'attribute',
            'attribute_title', 'is_filterable', 'is_option_selector'
        ]


class BrandProductTypeStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for brand-product type associations
    """
    brand_title = serializers.CharField(source='brand.title', read_only=True)
    product_type_title = serializers.CharField(source='product_type.title', read_only=True)

    class Meta:
        model = BrandProductTypeProxy
        fields = [
            'id', 'brand', 'brand_title', 'product_type', 'product_type_title'
        ]


class ProductAttributeValueStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for product-level attribute value associations
    """
    attribute_value_text = serializers.CharField(source='attribute_value.attribute_value', read_only=True)
    attribute_title = serializers.CharField(source='attribute_value.attribute.title', read_only=True)
    product_title = serializers.CharField(source='product.title', read_only=True)

    class Meta:
        model = ProductAttributeValueProxy
        fields = [
            'id', 'product', 'product_title', 'attribute_value',
            'attribute_value_text', 'attribute_title'
        ]


class ProductVariantAttributeValueStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for product variant attribute value associations
    """
    attribute_value_text = serializers.CharField(source='attribute_value.attribute_value', read_only=True)
    attribute_title = serializers.CharField(source='attribute_value.attribute.title', read_only=True)
    product_variant_sku = serializers.CharField(source='product_variant.sku', read_only=True)

    class Meta:
        model = ProductVariantAttributeValueProxy
        fields = [
            'id', 'product_variant', 'product_variant_sku', 'attribute_value',
            'attribute_value_text', 'attribute_title', 'is_active', 'order'
        ]


class ProductVariantAttributeValueDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for product variant attribute value associations
    Used for getting all attribute values associated with a specific product variant
    """
    attribute_value = AttributeValueStaffSerializer(read_only=True)
    attribute = AttributeStaffSerializer(source='attribute_value.attribute', read_only=True)

    class Meta:
        model = ProductVariantAttributeValueProxy
        fields = [
            'id', 'attribute_value', 'attribute', 'is_active', 'order'
        ]


class ReviewStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff review management
    """
    customer_email = serializers.CharField(source='customer.user.email', read_only=True)
    product_title = serializers.CharField(source='product.title', read_only=True)

    class Meta:
        model = ReviewProxy
        fields = [
            'id', 'title', 'product', 'product_title', 'customer',
            'customer_email', 'description', 'rating', 'posted_at'
        ]
        read_only_fields = ['posted_at']


class DiscountStaffSerializer(serializers.ModelSerializer):
    """
    Serializer for staff discount management
    """
    product_variants_count = serializers.SerializerMethodField()
    is_currently_valid = serializers.SerializerMethodField()

    class Meta:
        model = DiscountProxy
        fields = [
            'id', 'name', 'discount_percentage', 'start_date', 'end_date',
            'is_active', 'product_variants', 'product_variants_count',
            'is_currently_valid'
        ]

    def get_product_variants_count(self, obj):
        return obj.product_variants.count()

    def get_is_currently_valid(self, obj):
        return obj.is_valid()


class BulkProductSerializer(serializers.Serializer):
    """
    Serializer for bulk product operations
    """
    operation_type = serializers.ChoiceField(choices=[
        ('update', 'Update'),
        ('delete', 'Delete'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
        ('assign_category', 'Assign Category'),
    ])
    product_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1
    )
    data = serializers.JSONField(required=False)

    def validate(self, attrs):
        operation_type = attrs['operation_type']
        data = attrs.get('data', {})

        if operation_type == 'assign_category' and 'category_id' not in data:
            raise serializers.ValidationError(
                "category_id is required for assign_category operation"
            )

        return attrs


class AttributeAssociationSerializer(serializers.Serializer):
    """
    Serializer for individual attribute association
    """
    attribute_id = serializers.IntegerField()
    is_filterable = serializers.BooleanField(default=False)
    is_option_selector = serializers.BooleanField(default=False)

    def validate_attribute_id(self, value):
        try:
            AttributeProxy.objects.get(id=value)
        except AttributeProxy.DoesNotExist:
            raise serializers.ValidationError("Attribute does not exist")
        return value


class BulkAttributeAssociationSerializer(serializers.Serializer):
    """
    Serializer for bulk attribute associations
    """
    attributes = AttributeAssociationSerializer(many=True, min_length=1)


class BulkBrandProductTypeSerializer(serializers.Serializer):
    """
    Serializer for bulk brand-product type associations
    """
    brand_id = serializers.IntegerField()
    product_type_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1
    )

    def validate_brand_id(self, value):
        try:
            BrandProxy.objects.get(id=value)
        except BrandProxy.DoesNotExist:
            raise serializers.ValidationError("Brand does not exist")
        return value

    def validate_product_type_ids(self, value):
        existing_ids = ProductTypeProxy.objects.filter(id__in=value).values_list('id', flat=True)
        if len(existing_ids) != len(value):
            missing_ids = set(value) - set(existing_ids)
            raise serializers.ValidationError(f"Product types do not exist: {missing_ids}")
        return value


class BulkProductAttributeValueSerializer(serializers.Serializer):
    """
    Serializer for bulk product attribute value associations
    """
    product_id = serializers.IntegerField()
    attribute_value_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1
    )

    def validate_product_id(self, value):
        try:
            ProductProxy.objects.get(id=value)
        except ProductProxy.DoesNotExist:
            raise serializers.ValidationError("Product does not exist")
        return value

    def validate_attribute_value_ids(self, value):
        existing_ids = AttributeValueProxy.objects.filter(id__in=value).values_list('id', flat=True)
        if len(existing_ids) != len(value):
            missing_ids = set(value) - set(existing_ids)
            raise serializers.ValidationError(f"Attribute values do not exist: {missing_ids}")
        return value


class BulkVariantAttributeValueSerializer(serializers.Serializer):
    """
    Serializer for bulk variant attribute value associations
    """
    product_variant_id = serializers.IntegerField()
    attribute_value_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1
    )

    def validate_product_variant_id(self, value):
        try:
            ProductVariantProxy.objects.get(id=value)
        except ProductVariantProxy.DoesNotExist:
            raise serializers.ValidationError("Product variant does not exist")
        return value

    def validate_attribute_value_ids(self, value):
        existing_ids = AttributeValueProxy.objects.filter(id__in=value).values_list('id', flat=True)
        if len(existing_ids) != len(value):
            missing_ids = set(value) - set(existing_ids)
            raise serializers.ValidationError(f"Attribute values do not exist: {missing_ids}")
        return value


class ProductAuditSerializer(serializers.ModelSerializer):
    """
    Serializer for product audit logs
    """
    staff_user_email = serializers.CharField(source='staff_user.email', read_only=True)
    product_title = serializers.CharField(source='product.title', read_only=True)

    class Meta:
        model = ProductAudit
        fields = [
            'id', 'product', 'product_title', 'staff_user', 'staff_user_email',
            'action', 'changes', 'ip_address', 'user_agent', 'timestamp', 'notes'
        ]
        read_only_fields = ['timestamp']


class BulkOperationSerializer(serializers.ModelSerializer):
    """
    Serializer for bulk operation tracking
    """
    staff_user_email = serializers.CharField(source='staff_user.email', read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()

    class Meta:
        model = BulkProductOperation
        fields = [
            'id', 'operation_id', 'staff_user', 'staff_user_email',
            'operation_type', 'status', 'total_items', 'processed_items',
            'failed_items', 'progress_percentage', 'started_at',
            'completed_at', 'duration', 'error_message'
        ]
        read_only_fields = ['operation_id', 'started_at']

    def get_progress_percentage(self, obj):
        return obj.progress_percentage

    def get_duration(self, obj):
        if obj.completed_at:
            delta = obj.completed_at - obj.started_at
            return delta.total_seconds()
        return None


class ProductVariantCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating product variants (without a product field)
    Used specifically for creating variants alongside a new product
    """

    class Meta:
        model = ProductVariantProxy
        fields = [
            'price', 'price_label', 'sku', 'stock_qty',
            'is_active', 'weight', 'condition'
        ]
        # Exclude the 'product' field since it will be set programmatically


class ProductCreateWithVariantsSerializer(serializers.Serializer):
    """
    Serializer for creating products with variants in one request
    """
    product = ProductStaffSerializer()
    variants = serializers.ListField(
        child=ProductVariantCreateSerializer(),
        min_length=1,
        required=False
    )

    def create(self, validated_data):
        from .services import ProductService

        product_data = validated_data['product']
        variants_data = validated_data.get('variants', [])
        staff_user = self.context['request'].user

        product, variants = ProductService.create_product_with_variants(
            product_data=product_data,
            variants_data=variants_data,
            staff_user=staff_user,
            request=self.context['request']
        )

        return {
            'product': product,
            'variants': variants
        }


class CategoryMoveSerializer(serializers.Serializer):
    """
    Serializer for moving categories in the tree
    """
    new_parent_id = serializers.IntegerField(required=False, allow_null=True)

    def validate_new_parent_id(self, value):
        if value is not None:
            try:
                CategoryProxy.objects.get(id=value)
            except CategoryProxy.DoesNotExist:
                raise serializers.ValidationError("Parent category does not exist")
        return value


class ProductAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for product analytics data
    """
    total_products = serializers.IntegerField()
    active_products = serializers.IntegerField()
    products_with_variants = serializers.IntegerField()
    products_with_reviews = serializers.IntegerField()
    average_rating = serializers.FloatField()

    # Additional analytics fields
    top_categories = serializers.ListField(child=serializers.DictField(), required=False)
    top_brands = serializers.ListField(child=serializers.DictField(), required=False)
    stock_alerts = serializers.ListField(child=serializers.DictField(), required=False)


class OrderUpdateSerializer(serializers.Serializer):
    """
    Serializer for individual order update
    """
    id = serializers.IntegerField()
    order = serializers.IntegerField(min_value=1)


class BulkOrderUpdateSerializer(serializers.Serializer):
    """
    Serializer for bulk order updates
    """
    order_updates = OrderUpdateSerializer(many=True, min_length=1)
