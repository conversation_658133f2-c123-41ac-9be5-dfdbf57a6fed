from django.db import models
from django.contrib import admin
from django.db.models import Avg
from django.utils.text import slugify
from django.utils import timezone
from django.utils.timezone import now
from decimal import Decimal
from mptt.models import MPTTModel, TreeForeignKey
from mptt.managers import TreeManager
from cloudinary.models import CloudinaryField
from ordered_model.models import OrderedModel
from utils.ordering import OrderField
from apps.customers.models import Customer
from django.core.exceptions import ValidationError
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from utils.shared_classes import IsActiveQuerySet


class CategoryManager(TreeManager):
    def get_queryset(self):
        return IsActiveQuerySet(self.model, using=self._db)

    def is_active(self):
        return self.get_queryset().is_active()


class Category(MPTTModel):
    # objects = IsActiveQuerySet().as_manager()
    objects = CategoryManager()
    # Adding 'db_index=True' improves the speed of data retrieval operations
    title = models.CharField(max_length=50)
    slug = models.SlugField(max_length=255, unique=True, blank=True)  # Make slug non-unique initially
    is_active = models.BooleanField(default=True)
    parent = TreeForeignKey('self', on_delete=models.PROTECT, null=True, blank=True)

    class MPTTMeta:
        order_insertion_by = ['title']

    def __str__(self):
        full_path = [self.title]
        k = self.parent
        while k is not None:
            full_path.append(k.title)
            k = k.parent
        return ' > '.join(full_path[::-1])

    def save(self, *args, **kwargs):
        # Check if this is a new instance (creation) or updating an existing one
        if not self.pk:  # pk is None if the object is not yet saved, meaning it's being created
            if not self.slug:
                # Generate slug only if it's not manually provided (only on creation)
                self.slug = slugify(self.title)
            # Check if there is a parent and adjust the slug accordingly
            if self.parent:
                self.slug = f'{self.parent.slug}-{self.slug}'
        else:
            # This is an update action
            if not self.slug:  # If the slug is empty, regenerate it from the title
                self.slug = slugify(self.title)
            # Optionally, you can still handle slug concatenation with the parent
            if self.parent and not self.slug.startswith(f'{self.parent.slug}-'):
                self.slug = f'{self.parent.slug}-{self.slug}'

        super().save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "Categories"
        unique_together = ('slug', 'parent')


class Attribute(models.Model):
    title = models.CharField(max_length=100)

    def __str__(self):
        return self.title


class AttributeValue(models.Model):
    attribute_value = models.CharField(max_length=100)
    attribute = models.ForeignKey(
        # With this related name, we can get all attribute values connected to an attribute
        Attribute, on_delete=models.CASCADE, related_name='attribute_value')
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f'{self.attribute.title}-{self.attribute_value}'

    @admin.display(ordering='attribute__title')
    def title(self):
        return f'{self.attribute.title} - {self.attribute_value}'

    class Meta:
        verbose_name_plural = "Attribute Values"


class ProductType(models.Model):
    title = models.CharField(max_length=100)
    # What's the point of defined a parent product type here?
    parent = models.ForeignKey(
        'self', on_delete=models.PROTECT, null=True, blank=True)
    attribute = models.ManyToManyField(
        Attribute,
        through='ProductTypeAttribute',
        related_name='product_type_attribute'
    )

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = "Product Type"


# Customized auto-generated link table associate ProductType and Attribute models
class ProductTypeAttribute(models.Model):
    # ForeignKey to ProductType, establishing a many-to-one relationship
    product_type = models.ForeignKey(
        ProductType,
        # If a ProductType is deleted, its related ProductTypeAttributes are also deleted
        on_delete=models.CASCADE,
        # Related name to access this relationship from ProductType
        related_name='product_type_attribute_pt'
    )
    attribute = models.ForeignKey(
        Attribute,
        on_delete=models.CASCADE,
        related_name='product_type_attribute_a'
    )
    is_filterable = models.BooleanField(default=False)
    is_option_selector = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.attribute.title} is an attribute of {self.product_type.title}'

    class Meta:
        unique_together = ('product_type', 'attribute')
        verbose_name = "Product Type Attribute"


# class Promotion(models.Model):
#     description = models.CharField(max_length=255)
#     discount = models.FloatField()

class Brand(models.Model):
    title = models.CharField(max_length=100)
    slug = models.SlugField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)
    product_types = models.ManyToManyField(ProductType, through='BrandProductType', related_name='brands')

    def __str__(self):
        return self.title


class Product(models.Model):
    objects = IsActiveQuerySet().as_manager()
    title = models.CharField(max_length=100, db_index=True)
    slug = models.SlugField(max_length=255, db_index=True)
    brand = models.ForeignKey(Brand, on_delete=models.PROTECT, db_index=True)
    # product_id = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    is_digital = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    # featured = models.BooleanField(db_index=True)
    # promotions = models.ManyToManyField(Promotion)
    product_type = models.ForeignKey(
        ProductType, on_delete=models.PROTECT, related_name='product_type', db_index=True
    )
    attribute_value = models.ManyToManyField(
        AttributeValue,
        through='ProductAttributeValue',
        related_name='product_attr_value',
        blank=True
    )
    category = models.ForeignKey(Category, on_delete=models.PROTECT, related_name='products')
    average_rating = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True, editable=False)

    def __str__(self):
        return self.title

    def update_average_rating(self):
        # Calculate the new average rating based on related reviews
        avg_rating = self.reviews.aggregate(Avg('rating'))['rating__avg'] or 0.0
        self.average_rating = avg_rating
        self.save(update_fields=['average_rating'])


# Customized auto generated link table
class ProductAttributeValue(models.Model):
    attribute_value = models.ForeignKey(
        AttributeValue, on_delete=models.CASCADE, related_name='product_value_av')
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name='product_value_pv')

    class Meta:
        unique_together = ('attribute_value', 'product',)


class ProductVariant(OrderedModel):
    # objects = IsActiveQuerySet().as_manager() # This doesn't work, you have applied filters in views
    price = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('1'))]
    )
    price_label = models.ForeignKey(
        AttributeValue,
        on_delete=models.CASCADE,
        # related_name='variant_price_labels',
        # limit_choices_to={'attribute__title': 'YourDesiredAttributeTitle'}
        blank=True,
        null=True
    )
    # discount = models.FloatField()
    sku = models.CharField(max_length=100, unique=True)
    stock_qty = models.PositiveIntegerField()
    is_active = models.BooleanField(default=True)
    weight = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Please add the weight in grams."
    )
    # order: order of product variants
    # order = OrderField(unique_for_field='product', blank=True)
    CONDITION_CHOICES = (
        ('New', 'New'),
        ('Used', 'Used'),
    )
    condition = models.CharField(
        max_length=4, choices=CONDITION_CHOICES, default='New')
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name='product_variant')
    attribute_value = models.ManyToManyField(
        AttributeValue,
        through='ProductVariantAttributeValue',
        related_name='product_variant_attribute_value',
        blank=True,
    )
    # Is the product type necessary here?
    # product_type = models.ForeignKey(
    #     ProductType, on_delete=models.PROTECT, related_name='product_variant_type')
    order_with_respect_to = 'product'
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True, editable=False)

    def get_discounted_price(self):
        # Fetch active discounts for this variant
        active_discounts = self.discounts.filter(
            is_active=True, start_date__lte=now(), end_date__gte=now()
        ).order_by('-discount_percentage')  # Prioritize highest discount

        if active_discounts.exists():
            discount = active_discounts.first()  # Apply the highest discount
            # Convert discount percentage to Decimal
            discount_multiplier = Decimal('1') - Decimal(str(discount.discount_percentage)) / Decimal('100')
            return self.price * discount_multiplier

        # No active discount
        return 0.0

    def save(self, *args, **kwargs):
        self.full_clean()
        return super(ProductVariant, self).save(*args, **kwargs)

    def __str__(self):
        return f''

    # def __str__(self):
    #     return f'{self.product.title} - {self.price} variant'

    class Meta(OrderedModel.Meta):
        ordering = ['order']
        verbose_name = 'Product Variant'


# Customized auto generated link table
class ProductVariantAttributeValue(models.Model):
    objects = IsActiveQuerySet().as_manager()
    attribute_value = models.ForeignKey(
        AttributeValue, on_delete=models.CASCADE, related_name='product_attribute_value_av')
    product_variant = models.ForeignKey(
        ProductVariant, on_delete=models.CASCADE, related_name='product_attribute_value_pv')
    is_active = models.BooleanField(default=True)
    order = OrderField(unique_for_field='product_variant', help_text="Display order for this attribute value")

    class Meta:
        unique_together = ('attribute_value', 'product_variant',)
        verbose_name = 'Product Variant Attribute Value'
        ordering = ['order', 'attribute_value__attribute__title']

    def __str__(self):
        return f'"{self.attribute_value}" is an attribute value of "{self.product_variant}"'

    def clean(self):
        # Check if this is an existing record (has an ID) or a new one
        if self.pk:
            # This is an update to an existing record
            # We need to check if the attribute is being changed to one that already exists
            # Get the current attribute for this record
            current_attribute_id = ProductVariantAttributeValue.objects.get(pk=self.pk).attribute_value.attribute.id

            # If we're changing to a different attribute type
            if self.attribute_value.attribute.id != current_attribute_id:
                # Check if the new attribute type already exists for this product variant
                iqs = Attribute.objects.filter(
                    attribute_value__product_variant_attribute_value=self.product_variant
                ).values_list('pk', flat=True)

                if self.attribute_value.attribute.id in list(iqs):
                    raise ValidationError('Duplicate attribute exists')
        else:
            # This is a new record
            # Check if this exact attribute value already exists for this product variant
            qs = (
                ProductVariantAttributeValue.objects.filter(
                    attribute_value=self.attribute_value
                ).filter(product_variant=self.product_variant).exists()
            )

            # If this exact attribute value doesn't exist yet
            if not qs:
                # Check if an attribute of the same type already exists for this product variant
                iqs = Attribute.objects.filter(
                    attribute_value__product_variant_attribute_value=self.product_variant
                ).values_list('pk', flat=True)

                if self.attribute_value.attribute.id in list(iqs):
                    raise ValidationError('Duplicate attribute exists')

    def save(self, *args, **kwargs):
        self.full_clean()
        return super(ProductVariantAttributeValue, self).save(*args, **kwargs)


class BrandProductType(models.Model):
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE)
    product_type = models.ForeignKey(ProductType, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('brand', 'product_type')
        verbose_name = 'Brand Product Type'

    def __str__(self):
        return f"{self.brand.title} - {self.product_type.title}"


class ProductImage(models.Model):
    alternative_text = models.CharField(max_length=100)
    # image = models.ImageField(upload_to='store/images', default='test.jpg')
    image = CloudinaryField('image')
    product_variant = models.ForeignKey(
        ProductVariant, on_delete=models.CASCADE, related_name='product_image'
    )
    # order: order of product images
    order = OrderField(unique_for_field='product_variant', blank=True)

    def clean(self):
        # Only check for duplicate order if the order is explicitly set
        if self.order is not None:
            qs = ProductImage.objects.filter(product_variant=self.product_variant)
            for obj in qs:
                if self.id != obj.id and self.order == obj.order:
                    raise ValidationError('Duplicate value in ORDER.')

    def save(self, *args, **kwargs):
        # Don't validate the order field as it will be auto-generated
        # by the OrderField's pre_save method
        if not self.pk and not self.order:  # New instance without order
            # Skip validation and let OrderField handle it
            return super(ProductImage, self).save(*args, **kwargs)
        else:
            # For existing instances or when order is explicitly set
            self.full_clean()
            return super(ProductImage, self).save(*args, **kwargs)

    def __str__(self):
        return f'{self.product_variant.sku}_img'


class Review(models.Model):
    title = models.CharField(max_length=100)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='reviews')
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    description = models.TextField()
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    posted_at = models.DateField(auto_now_add=True)

    def __str__(self):
        return f'{self.title}'

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Update the product's average rating after saving the review
        self.product.update_average_rating()

    def delete(self, *args, **kwargs):
        product = self.product
        super().delete(*args, **kwargs)
        # Update the product's average rating after deleting the review
        product.update_average_rating()


class Discount(models.Model):
    name = models.CharField(max_length=100)
    discount_percentage = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=False)
    product_variants = models.ManyToManyField(
        'ProductVariant', related_name='discounts', blank=True
    )  # Link discounts to specific product variants

    def __str__(self):
        return f"{self.name} - {self.discount_percentage}%"

    def is_valid(self):
        """Check if the discount is active and within the date range."""
        now = timezone.now()
        return self.is_active and self.start_date <= now <= self.end_date
