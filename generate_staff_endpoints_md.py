import json

input_path = r'd:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\staff_endpoints_schema.json'
output_path = r'd:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\staff_endpoints.md'

with open(input_path, 'r', encoding='utf-8') as f:
    schema = json.load(f)

title = schema['info'].get('title', 'Staff Endpoints')

lines = [f'# {title}\n']

for path, methods in schema.get('paths', {}).items():
    for method, details in methods.items():
        # Compose endpoint line
        endpoint_line = f'**{method.upper()} {path}**'
        # Compose description
        description = details.get('description', '').strip()
        # Add to lines
        lines.append(endpoint_line)
        if description:
            lines.append(f'> {description}')
        lines.append('')  # Blank line for spacing

with open(output_path, 'w', encoding='utf-8') as f:
    f.write('\n'.join(lines))