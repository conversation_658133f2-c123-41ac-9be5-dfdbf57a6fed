# Authentication Architecture Migration

## Overview
This document describes the migration from dual authentication systems to a unified authentication approach.

## Previous Architecture (Problematic)
- **Core App**: `CustomJWTAuthentication` with `access` cookie
- **Staff App**: `StaffJWTAuthentication` with `admin_access_token` cookie
- **Issues**: 
  - Code duplication
  - ViewSets missing authentication classes
  - Two parallel systems to maintain
  - Developer confusion

## New Architecture (Unified)
- **Single Authentication**: Core app's `CustomJWTAuthentication` handles all users
- **Smart Token Lifetimes**: Different lifetimes based on user type
  - Staff users: 8 hours access, 7 days refresh
  - Regular users: 90 days access, 90 days refresh
- **Single Cookie System**: Uses `access` and `refresh` cookies for all users

## Changes Made

### Core App Enhancements
1. **Enhanced `CustomLoginView`**:
   - Detects staff users automatically
   - Sets appropriate token lifetimes
   - Uses single cookie system

2. **Enhanced `CustomTokenRefreshView`**:
   - Maintains staff token lifetimes on refresh
   - Automatic user type detection

3. **Enhanced `LogoutView`**:
   - Clears both new and legacy cookies
   - Works for all user types

### Staff App Simplification
1. **Removed Classes**:
   - `StaffJWTAuthentication`
   - `StaffLoginView`
   - `StaffTokenRefreshView`
   - `StaffLogoutView`
   - `StaffAccessToken`
   - `StaffRefreshToken`

2. **Updated ViewSets**:
   - Removed explicit `authentication_classes`
   - Now use default unified authentication
   - Added documentation comments

3. **Updated URLs**:
   - Removed staff-specific auth endpoints
   - Staff now uses core auth endpoints

## API Endpoints

### Authentication (Core App) - Now Consistent with /api/ prefix
- **Login**: `POST /api/auth/login/` (unified for all users)
- **Logout**: `POST /api/auth/logout/` (unified for all users)
- **Current User**: `GET /api/auth/me/`
- **Token Refresh**: `POST /api/auth/token/refresh/`
- **Token Verify**: `POST /api/auth/token/verify/`

### User Registration (Core App)
- **Initiate Registration**: `POST /api/auth/register/initiate/`
- **Verify Code**: `POST /api/auth/register/verify/`
- **Set Password**: `POST /api/auth/register/set-password/`

### Password Management (Core App)
- **Reset Request**: `POST /api/auth/password/reset/request/`
- **Reset Confirm**: `POST /api/auth/password/reset/confirm/`
- **Change Password**: `POST /api/auth/password/change/`

### Profile Management (Core App)
- **Change Auth Method**: `POST /api/auth/profile/change-auth-method/`
- **Update Contact**: `POST /api/auth/profile/contact/update/`
- **Verify Contact**: `POST /api/auth/profile/contact/verify/`

### Social Authentication (Core App)
- **Google Login**: `POST /api/auth/social/google/`
- **Facebook Login**: `POST /api/auth/social/facebook/`

### Staff Authorization (Staff App)
- **Current User**: `GET /api/staff/auth/user/`
- **Permissions**: `GET /api/staff/auth/permissions/`
- **Check Permission**: `POST /api/staff/auth/check-permission/`

## Benefits
1. **Single Source of Truth**: One authentication system
2. **Automatic Fallback**: No need to specify auth classes
3. **Reduced Complexity**: Less code to maintain
4. **Better DX**: Developers don't need to remember which auth to use
5. **Consistent Behavior**: Same authentication flow for all users

## URL Structure Changes
As part of this migration, we also improved URL consistency:

### Before (Inconsistent)
- Core app: `/auth/users/login/` (no `/api/` prefix)
- Other apps: `/api/products/`, `/api/staff/`, etc.

### After (Consistent)
- Core app: `/api/auth/login/` (now has `/api/` prefix)
- Other apps: `/api/products/`, `/api/staff/`, etc. (unchanged)

See `URL_MIGRATION_GUIDE.md` for complete URL mapping.

## Migration Notes
- Frontend should update to use new core auth endpoints: `/api/auth/login/`, `/api/auth/logout/`
- Staff-specific authorization endpoints remain in staff app
- All existing permissions and authorization logic unchanged
- Token lifetimes automatically applied based on user type
- URL structure now consistent across all apps

## Testing
Test the orders endpoint that was failing:
```
GET /api/staff/orders/orders/?page=1&page_size=20&ordering=-created_at
```

Should now work with regular `access` cookie instead of `admin_access_token`.
