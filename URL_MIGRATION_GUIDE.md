# URL Structure Migration Guide

## Overview
This document outlines the URL structure changes made to improve consistency and organization across the project.

## Changes Made

### 1. Consistent API Prefix
All API endpoints now use the `/api/` prefix for consistency:

**Before:**
```
/auth/users/login/          # Core app (inconsistent)
/api/products/              # Products app
/api/staff/                 # Staff app
/api/orders/                # Orders app
```

**After:**
```
/api/auth/login/            # Core app (now consistent)
/api/products/              # Products app (unchanged)
/api/staff/                 # Staff app (unchanged)
/api/orders/                # Orders app (unchanged)
```

### 2. Reorganized Core App URLs
The core app URLs have been restructured for better organization:

#### Authentication Endpoints
| Endpoint | Old URL | New URL |
|----------|---------|---------|
| Login | `/auth/users/login/` | `/api/auth/login/` |
| Logout | `/auth/users/logout/` | `/api/auth/logout/` |
| Current User | `/auth/users/me/` | `/api/auth/me/` |

#### JWT Token Management
| Endpoint | Old URL | New URL |
|----------|---------|---------|
| Create Token | `/auth/jwt/create/` | `/api/auth/token/create/` |
| Refresh Token | `/auth/jwt/refresh/` | `/api/auth/token/refresh/` |
| Verify Token | `/auth/jwt/verify/` | `/api/auth/token/verify/` |

#### User Registration
| Endpoint | Old URL | New URL |
|----------|---------|---------|
| Initiate Registration | `/auth/initiate-registration/` | `/api/auth/register/initiate/` |
| Verify Code | `/auth/verify-code/` | `/api/auth/register/verify/` |
| Set Password | `/auth/set-password/` | `/api/auth/register/set-password/` |

#### Password Management
| Endpoint | Old URL | New URL |
|----------|---------|---------|
| Reset Request | `/auth/password-reset-request/` | `/api/auth/password/reset/request/` |
| Reset Confirm | `/auth/password-reset-confirm/` | `/api/auth/password/reset/confirm/` |
| Change Password | `/auth/change-password/` | `/api/auth/password/change/` |

#### Profile Management
| Endpoint | Old URL | New URL |
|----------|---------|---------|
| Change Auth Method | `/auth/change-primary-auth-method/` | `/api/auth/profile/change-auth-method/` |
| Update Contact | `/auth/add-contact/initiate/` | `/api/auth/profile/contact/update/` |
| Verify Contact | `/auth/add-contact/verify/` | `/api/auth/profile/contact/verify/` |

#### Social Authentication
| Endpoint | Old URL | New URL |
|----------|---------|---------|
| Google Login | `/auth/google/` | `/api/auth/social/google/` |
| Facebook Login | `/auth/facebook/` | `/api/auth/social/facebook/` |
| Facebook Complete | `/auth/facebook/complete/` | `/api/auth/social/facebook/complete/` |

### 3. Staff App URLs (Unchanged)
Staff app URLs remain the same but now use unified authentication:

| Endpoint | URL | Notes |
|----------|-----|-------|
| Current User | `/api/staff/auth/user/` | Uses unified auth |
| Permissions | `/api/staff/auth/permissions/` | Uses unified auth |
| Check Permission | `/api/staff/auth/check-permission/` | Uses unified auth |
| Orders | `/api/staff/orders/orders/` | **This was the failing endpoint** |

## Benefits

### 1. Consistency
- All API endpoints now use `/api/` prefix
- Logical grouping of related endpoints
- Consistent naming conventions

### 2. Better Organization
- Clear separation between authentication, registration, password, and profile management
- Hierarchical URL structure that's easy to understand
- Social auth endpoints grouped together

### 3. Maintainability
- Easier to add new endpoints in logical groups
- Clear patterns for future development
- Better API documentation structure

## Migration Notes for Frontend

### Authentication Changes
```javascript
// OLD
const loginUrl = '/auth/users/login/';
const logoutUrl = '/auth/users/logout/';
const refreshUrl = '/auth/jwt/refresh/';

// NEW
const loginUrl = '/api/auth/login/';
const logoutUrl = '/api/auth/logout/';
const refreshUrl = '/api/auth/token/refresh/';
```

### Registration Changes
```javascript
// OLD
const initiateUrl = '/auth/initiate-registration/';
const verifyUrl = '/auth/verify-code/';

// NEW
const initiateUrl = '/api/auth/register/initiate/';
const verifyUrl = '/api/auth/register/verify/';
```

### Staff Operations (No Change)
```javascript
// These remain the same
const staffOrdersUrl = '/api/staff/orders/orders/';
const staffUserUrl = '/api/staff/auth/user/';
```

## Testing the Fix

The original issue was:
```
GET /api/staff/orders/orders/?page=1&page_size=20&ordering=-created_at
```

This should now work because:
1. Staff users login via `/api/auth/login/` (unified authentication)
2. They get `access` and `refresh` cookies (not `admin_access_token`)
3. Staff ViewSets use default `CustomJWTAuthentication` (reads `access` cookie)
4. Authentication succeeds, authorization is checked via `IsStaffUser` permission

## Social Authentication Note

Social authentication URLs remain separate from the API:
- `/auth/social/` - Django Social Auth URLs (not API endpoints)
- `/api/auth/social/` - Our custom social auth API endpoints

This separation is intentional to distinguish between Django Social Auth's URLs and our custom API endpoints.
